const { Op } = require('sequelize');
const BaseRepository = require('./BaseRepository');
const { JobTitle } = require('../models');

/**
 * JobTitlesRepository - Repository for job title read operations
 * Extends BaseRepository with job title-specific filtering and scoping
 */
class JobTitlesRepository extends BaseRepository {
  constructor() {
    super(JobTitle);
  }

  /**
   * Filter job titles by search term (searches in name)
   * Usage: /api/v1/job_titles?search=developer
   * @param {string} search - Search term
   * @returns {Object} Where condition for search filter
   */
  filterBySearch(search) {
    if (!search) return null;

    return {
      name: {
        [Op.iLike]: `%${search}%`,
      },
    };
  }
}

module.exports = JobTitlesRepository;
