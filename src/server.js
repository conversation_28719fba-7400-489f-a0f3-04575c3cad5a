const app = require('./app');
const config = require('./config/config');
const { sequelize } = require('./config/sequelize');

const PORT = config.port;
const HOST = config.host;

let isShuttingDown = false;

// Graceful shutdown handler
const gracefulShutdown = async (server, signal, error) => {
  if (isShuttingDown) {
    console.log('Shutdown already in progress. Ignoring subsequent signals.');
    return;
  }

  isShuttingDown = true;
  console.log(`\nReceived ${signal}. Starting graceful shutdown...`);

  // If the shutdown was triggered by an error, log it and set the exit code to 1
  if (error) {
    console.error('Shutdown triggered by error:', error);
    process.exitCode = 1;
  }

  try {
    // 1. Close the server to stop accepting new connections
    await new Promise((resolve, reject) => {
      server.close(err => {
        if (err) {
          return reject(err);
        }
        console.log('Server closed successfully.');
        resolve();
      });
    });

    // 2. Close database connections
    await sequelize.close();
    console.log('Database connections closed.');
  } catch (shutdownError) {
    console.error('Error during graceful shutdown:', shutdownError);
    process.exitCode = 1; // Mark the exit as a failure
  } finally {
    console.log('Graceful shutdown complete. Process will exit.');
    // The process will now exit naturally because the event loop is empty.
    // The exit code will be 0 (success) or 1 (failure) based on `process.exitCode`.
  }
};

// Start server
const startServer = async () => {
  try {
    // Test database connection
    await sequelize.authenticate();
    console.log('Database connection established successfully.');

    // Start listening
    const server = app.listen(PORT, HOST, () => {
      console.log(`🚀 Server is running on http://${HOST}:${PORT}`);
      console.log(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
      console.log(`🔍 Health check: http://${HOST}:${PORT}/api/v1/health`);
    });

    // Handle graceful shutdown signals (e.g., from Ctrl+C or kill)
    process.on('SIGTERM', () => gracefulShutdown(server, 'SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown(server, 'SIGINT'));

    // Handle uncaught exceptions - a last resort
    process.on('uncaughtException', error => {
      console.error('Uncaught Exception:', error.message, error.stack);
      gracefulShutdown(server, 'UNCAUGHT_EXCEPTION', error);
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      console.error('Unhandled Rejection at:', promise, 'reason:', reason);
      // In modern Node.js, an unhandled rejection will crash the process by default.
      // It's best to treat it like an uncaught exception.
      gracefulShutdown(server, 'UNHANDLED_REJECTION', reason);
    });

    return server;
  } catch (error) {
    console.error('Failed to start server:', error);
    // Throwing the error is the correct way to signal failure from an async function.
    // This allows the caller to handle the startup failure.
    throw error;
  }
};

// Start the server if this file is run directly
if (require.main === module) {
  startServer().catch(() => {
    // The error is already logged by startServer's catch block.
    // We set the exit code here to ensure the shell knows the process failed.
    process.exitCode = 1;
  });
}

module.exports = { startServer };
