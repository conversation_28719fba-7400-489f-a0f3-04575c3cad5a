const express = require('express');
const usersController = require('../controllers/UsersController');
const { authenticateToken, requireRole } = require('../middlewares/auth');

const router = express.Router();

/**
 * @route GET /api/v1/users
 * @desc Get all users (admin only)
 * @access Private (Admin)
 */
router.get('/', authenticateToken, requireRole('admin'), usersController.index);

module.exports = router;
