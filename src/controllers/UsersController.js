const ApiController = require('./ApiController');
const UserService = require('../services/UserService');
const UsersIndexInput = require('../inputs/UsersIndexInput');
const UserOutput = require('../outputs/UserOutput');

class UsersController extends ApiController {
  constructor() {
    super();
    this.service = new UserService();
  }

  /**
   * Get all users (admin only)
   * @param {Object} req - Express request object
   * @param {Object} res - Express response object
   */
  index = this.createMethod(async (req, res) => {
    const input = new UsersIndexInput(req.query);
    input.validate();

    const result = await this.service.findAll(input.output());
    const output = new UserOutput(result.users, { pagination: result.pagination });

    output.renderJsonArray(res);
  });
}

module.exports = new UsersController();
