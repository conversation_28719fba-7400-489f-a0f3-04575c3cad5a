const AppService = require('./AppService');
const UsersRepository = require('../repositories/UsersRepository');

class UserService extends AppService {
  constructor() {
    super();
    this.repository = new UsersRepository();
  }

  /**
   * Get all users with pagination
   * @param {Object} params - Query params (page, limit, etc.)
   * @returns {Object} - Users array and pagination info
   */
  async findAll(params = {}) {
    const { rows, pagination } = await this.repository.findAll(params);
    return { users: rows, pagination };
  }
}

module.exports = UserService;
