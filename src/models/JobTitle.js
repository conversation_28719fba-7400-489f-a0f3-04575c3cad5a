'use strict';
const { DataTypes } = require('sequelize');
const AppModel = require('./AppModel');

class JobTitle extends AppModel {
  /**
   * Associations with other models
   */
  static associate(_models) {
    // No associations needed for this model currently
  }

  /**
   * Model schema
   */
  static schema() {
    return {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
        unique: {
          msg: 'Job title name already exists',
        },
        validate: {
          notEmpty: {
            msg: 'Job title name cannot be empty',
          },
          len: {
            args: [2, 100],
            msg: 'Job title name must be between 2 and 100 characters',
          },
        },
      },
      prefilled_details: {
        type: DataTypes.JSONB,
        allowNull: true,
        field: 'prefilled_details',
        validate: {
          isValidJSON(value) {
            if (value !== null && typeof value !== 'object') {
              throw new Error('Prefilled details must be a valid JSON object');
            }
          },
        },
      },
    };
  }

  /**
   * Lifecycle hooks
   */
  static hooks() {
    return {
      beforeCreate: jobTitle => {
        jobTitle.created_at = new Date();
        jobTitle.updated_at = new Date();
      },
      beforeUpdate: jobTitle => {
        jobTitle.updated_at = new Date();
      },
    };
  }

  /**
   * Additional model options
   */
  static options() {
    return {
      indexes: [
        {
          unique: true,
          fields: ['name'],
        },
      ],
    };
  }
}

module.exports = JobTitle;
