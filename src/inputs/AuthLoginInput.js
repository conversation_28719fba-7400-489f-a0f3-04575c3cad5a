const ApplicationInput = require('./ApplicationInput');

/**
 * Input validation class for user login
 */
class AuthLoginInput extends ApplicationInput {
  /**
   * Define the JSON schema for login validation
   * @returns {Object} JSON schema object
   */
  schema() {
    return {
      type: 'object',
      properties: {
        email: {
          type: 'string',
          format: 'email',
          minLength: 1,
          maxLength: 255,
        },
        password: {
          type: 'string',
          minLength: 1,
          maxLength: 255,
        },
      },
      required: ['email', 'password'],
    };
  }

  output() {
    const data = super.output();
    data.email = data.email.toLowerCase().trim();
    return data;
  }
}

module.exports = AuthLoginInput;
