{"name": "paragon-api", "version": "1.0.0", "description": "", "license": "ISC", "author": "", "type": "commonjs", "main": "index.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest --maxConcurrency 1", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --reporters=default --reporters=jest-junit", "test:setup": "node scripts/setup-test-db.js", "lint": "eslint .", "lint:fix": "eslint . --fix", "lint:check": "eslint . --max-warnings 0", "format": "prettier --write .", "format:check": "prettier --check .", "prepare": "husky", "db:migrate": "sequelize-cli db:migrate", "db:migrate:undo": "sequelize-cli db:migrate:undo", "db:reset": "sequelize-cli db:migrate:undo:all && sequelize-cli db:migrate && sequelize-cli db:seed:all", "db:create": "sequelize-cli db:create", "db:seed": "sequelize-cli db:seed:all"}, "dependencies": {"ajv": "^8.17.1", "ajv-formats": "^3.0.1", "bcrypt": "^6.0.0", "compression": "^1.8.1", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "pg": "^8.16.3", "pg-hstore": "^2.3.4", "sequelize": "^6.37.7"}, "devDependencies": {"@eslint/js": "^9.33.0", "@eslint/json": "^0.13.1", "eslint": "^9.33.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "globals": "^16.3.0", "husky": "^9.1.7", "jest": "^30.0.5", "jest-junit": "^16.0.0", "lint-staged": "^16.1.5", "nodemon": "^3.1.10", "prettier": "^3.6.2", "sequelize-cli": "^6.6.3", "supertest": "^7.1.4"}, "lint-staged": {"*.{js,mjs,cjs}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}}