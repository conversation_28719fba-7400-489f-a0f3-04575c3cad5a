# Middleware

Middleware functions execute during the request-response cycle and can modify
requests, responses, or terminate the cycle. The application uses both built-in
Express middleware and custom middleware for various purposes.

## Built-in Middleware

The application uses several Express and third-party middleware:

### Security and Performance

```javascript
// src/app.js
const express = require('express');
const cors = require('cors');
const compression = require('compression');
const helmet = require('helmet');

// Security headers
app.use(helmet());
app.disable('x-powered-by');

// Compression
app.use(compression());

// CORS configuration
app.use(
  cors({
    origin: process.env.CORS_ORIGIN || '*',
    credentials: true,
  }),
);

// Body parsing
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
```

## Rate Limiting Middleware

The application includes a comprehensive rate limiting system:

### Rate Limiter Configuration

```javascript
// src/middlewares/rateLimiter.js
const rateLimit = require('express-rate-limit');

/**
 * Create a rate limiter with custom configuration
 * @param {Object} options - Rate limiting options
 * @returns {Function} Express middleware function
 */
const createRateLimiter = (options = {}) => {
  const windowMs =
    options.windowMs || parseInt(process.env.RATE_LIMIT_WINDOW_MS, 10) || 15 * 60 * 1000;
  const maxRequests =
    options.max || parseInt(process.env.RATE_LIMIT_MAX_REQUESTS, 10) || 100;

  const config = {
    windowMs,
    max: maxRequests,
    skipSuccessfulRequests: options.skipSuccessfulRequests !== undefined
      ? options.skipSuccessfulRequests
      : process.env.RATE_LIMIT_SKIP_SUCCESSFUL === 'true' || false,
    skipFailedRequests: options.skipFailedRequests !== undefined
      ? options.skipFailedRequests
      : process.env.RATE_LIMIT_SKIP_FAILED === 'true' || false,

    // Enable standard headers and disable legacy headers
    standardHeaders: true,
    legacyHeaders: false,

    // Custom handler for rate limit exceeded
    handler: (_req, res) => {
      const resetTime = new Date(Date.now() + windowMs);
      res.set({
        'X-RateLimit-Limit': maxRequests.toString(),
        'X-RateLimit-Remaining': '0',
        'X-RateLimit-Reset': Math.ceil(resetTime.getTime() / 1000).toString(),
      });

      res.status(429).json({
        error: 'Too Many Requests',
        message: 'Rate limit exceeded. Please try again later.',
        retryAfter: Math.ceil(windowMs / 1000),
      });
    },

    // Skip function for health checks
    skip: req => {
      if (req.path === '/api/v1/health') {
        return true;
      }

      const skipPaths = process.env.RATE_LIMIT_SKIP_PATHS
        ? process.env.RATE_LIMIT_SKIP_PATHS.split(',').map(path => path.trim())
        : [];

      return skipPaths.includes(req.path);
    },
  };

  // Create and wrap the rate limiter to add custom headers
  const limiter = rateLimit(config);

  return (req, res, next) => {
    // Store original send method and override to add headers
    const originalSend = res.send;
    res.send = function (data) {
      const resetTime = new Date(Date.now() + windowMs);
      const remaining = Math.max(0, maxRequests - (req.rateLimit?.used || 0));

      res.set({
        'X-RateLimit-Limit': maxRequests.toString(),
        'X-RateLimit-Remaining': remaining.toString(),
        'X-RateLimit-Reset': Math.ceil(resetTime.getTime() / 1000).toString(),
      });

      return originalSend.call(this, data);
    };

    limiter(req, res, next);
  };
};

// Pre-configured rate limiters
const rateLimiters = {
  // General API rate limiter
  general: createRateLimiter(),

  // Strict rate limiter for authentication endpoints
  auth: createRateLimiter({
    windowMs: parseInt(process.env.RATE_LIMIT_AUTH_WINDOW_MS, 10) || 15 * 60 * 1000,
    max: parseInt(process.env.RATE_LIMIT_AUTH_MAX_REQUESTS, 10) || 5,
    skipSuccessfulRequests: true, // Don't count successful logins
  }),

  // Lenient rate limiter for public endpoints
  public: createRateLimiter({
    windowMs: parseInt(process.env.RATE_LIMIT_PUBLIC_WINDOW_MS, 10) || 15 * 60 * 1000,
    max: parseInt(process.env.RATE_LIMIT_PUBLIC_MAX_REQUESTS, 10) || 200,
  }),
};

  // Lenient rate limiter for public endpoints
  public: createRateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 200, // 200 requests per window
  }),
};

module.exports = { createRateLimiter, rateLimiters };
```

### Using Rate Limiters

```javascript
// src/app.js
const { rateLimiters } = require('./middlewares/rateLimiter');

// Apply general rate limiting to all routes
app.use(rateLimiters.general);

// Apply specific rate limiting to route groups
app.use('/api/v1/auth', rateLimiters.auth, authRoutes);
app.use('/api/v1/public', rateLimiters.public, publicRoutes);
```

## Authentication Middleware

### JWT Authentication Middleware

```javascript
// src/middlewares/auth.js
const jwt = require('jsonwebtoken');
const { User } = require('../models');
const UnauthorizedError = require('../errors/UnauthorizedError');
const ForbiddenError = require('../errors/ForbiddenError');

/**
 * Verify JWT token and attach user to request
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      throw new UnauthorizedError('Access token required');
    }

    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);

    // Find user
    const user = await User.findByPk(decoded.userId);
    if (!user) {
      throw new UnauthorizedError('Invalid token');
    }

    // Check if user is active
    if (!user.isActive) {
      throw new ForbiddenError('Account is deactivated');
    }

    // Attach user to request
    req.user = user;
    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return next(new UnauthorizedError('Invalid token'));
    }
    if (error.name === 'TokenExpiredError') {
      return next(new UnauthorizedError('Token has expired'));
    }
    next(error);
  }
};

/**
 * Require specific role(s)
 * @param {string|Array} roles - Required role(s)
 * @returns {Function} Express middleware function
 */
const requireRole = roles => {
  const requiredRoles = Array.isArray(roles) ? roles : [roles];

  return (req, res, next) => {
    if (!req.user) {
      return next(new UnauthorizedError('Authentication required'));
    }

    if (!requiredRoles.includes(req.user.role)) {
      return next(new ForbiddenError('Insufficient permissions'));
    }

    next();
  };
};

/**
 * Optional authentication - attach user if token is valid
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      return next(); // No token, continue without user
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findByPk(decoded.userId);

    if (user && user.isActive) {
      req.user = user;
    }

    next();
  } catch (error) {
    // Invalid token, continue without user
    next();
  }
};

module.exports = {
  authenticateToken,
  requireRole,
  optionalAuth,
};
```

### Using Authentication Middleware

```javascript
// src/routes/users.js
const express = require('express');
const { authenticateToken, requireRole } = require('../middlewares/auth');
const userController = require('../controllers/UserController');

const router = express.Router();

// Public route - no authentication
router.get('/public', userController.publicInfo);

// Protected route - authentication required
router.get('/profile', authenticateToken, userController.profile);

// Admin only route
router.get(
  '/admin',
  authenticateToken,
  requireRole('admin'),
  userController.adminPanel,
);

// Multiple roles allowed
router.get(
  '/moderator',
  authenticateToken,
  requireRole(['admin', 'moderator']),
  userController.moderatorPanel,
);

module.exports = router;
```

## Validation Middleware

### Request Validation Middleware

```javascript
// src/middlewares/validation.js
const InvalidError = require('../errors/InvalidError');

/**
 * Validate request parameters
 * @param {Object} schema - Joi or custom validation schema
 * @param {string} property - Request property to validate ('body', 'params', 'query')
 * @returns {Function} Express middleware function
 */
const validateRequest = (schema, property = 'body') => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req[property]);

    if (error) {
      const details = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
      }));

      return next(new InvalidError('Validation failed', { details }));
    }

    // Replace request property with validated value
    req[property] = value;
    next();
  };
};

/**
 * Validate file uploads
 * @param {Object} options - File validation options
 * @returns {Function} Express middleware function
 */
const validateFile = (options = {}) => {
  const {
    required = false,
    maxSize = 5 * 1024 * 1024, // 5MB
    allowedTypes = ['image/jpeg', 'image/png', 'image/gif'],
  } = options;

  return (req, res, next) => {
    if (!req.file && required) {
      return next(new InvalidError('File is required'));
    }

    if (req.file) {
      // Check file size
      if (req.file.size > maxSize) {
        return next(
          new InvalidError(
            `File size must be less than ${maxSize / 1024 / 1024}MB`,
          ),
        );
      }

      // Check file type
      if (!allowedTypes.includes(req.file.mimetype)) {
        return next(
          new InvalidError(
            `File type must be one of: ${allowedTypes.join(', ')}`,
          ),
        );
      }
    }

    next();
  };
};

module.exports = {
  validateRequest,
  validateFile,
};
```

## Logging Middleware

### Request Logging Middleware

```javascript
// src/middlewares/logger.js
const morgan = require('morgan');

/**
 * Custom token for user ID
 */
morgan.token('user-id', req => {
  return req.user ? req.user.id : 'anonymous';
});

/**
 * Custom token for request ID
 */
morgan.token('request-id', req => {
  return req.id || 'unknown';
});

/**
 * Development logging format
 */
const developmentFormat =
  ':method :url :status :res[content-length] - :response-time ms - User: :user-id';

/**
 * Production logging format
 */
const productionFormat =
  ':remote-addr - :user-id [:date[clf]] ":method :url HTTP/:http-version" :status :res[content-length] ":referrer" ":user-agent" - :response-time ms';

/**
 * Create logger middleware based on environment
 * @returns {Function} Morgan middleware function
 */
const createLogger = () => {
  const format =
    process.env.NODE_ENV === 'production'
      ? productionFormat
      : developmentFormat;

  return morgan(format, {
    skip: (req, res) => {
      // Skip health check logs in production
      if (
        process.env.NODE_ENV === 'production' &&
        req.path === '/api/v1/health'
      ) {
        return true;
      }
      return false;
    },
  });
};

module.exports = { createLogger };
```

## Error Handling Middleware

### Global Error Handler

```javascript
// src/middlewares/errorHandler.js
const InvalidError = require('../errors/InvalidError');
const UnauthorizedError = require('../errors/UnauthorizedError');
const ForbiddenError = require('../errors/ForbiddenError');
const NotFoundError = require('../errors/NotFoundError');

/**
 * Global error handling middleware
 * This should be the last middleware in the stack
 */
const errorHandler = (error, req, res, next) => {
  let backtrace = [];
  if (process.env.NODE_ENV !== 'production') {
    backtrace = error.stack
      ? error.stack.split('\n').map(line => line.trim())
      : [];
  }

  // Log error for monitoring
  console.error('Global error handler:', error);

  // Handle known error types
  if (error instanceof InvalidError) {
    return res.status(400).json({
      error: error.message || 'Bad Request',
      details: error.details || [],
      backtrace,
    });
  }

  if (error instanceof UnauthorizedError) {
    return res.status(401).json({
      error: error.message || 'Unauthorized',
      details: [],
      backtrace,
    });
  }

  if (error instanceof ForbiddenError) {
    return res.status(403).json({
      error: error.message || 'Forbidden',
      details: [],
      backtrace,
    });
  }

  if (error instanceof NotFoundError) {
    return res.status(404).json({
      error: error.message || 'Not Found',
      details: [],
      backtrace,
    });
  }

  // Handle Sequelize errors
  if (error.name === 'SequelizeValidationError') {
    const details = error.errors.map(err => ({
      field: err.path,
      message: err.message,
    }));

    return res.status(400).json({
      error: 'Validation failed',
      details,
      backtrace,
    });
  }

  if (error.name === 'SequelizeUniqueConstraintError') {
    return res.status(400).json({
      error: 'Duplicate entry',
      details: [{ field: error.errors[0].path, message: 'Already exists' }],
      backtrace,
    });
  }

  // Handle unexpected errors
  res.status(500).json({
    error: 'Internal Server Error',
    details: [],
    backtrace,
  });
};

/**
 * 404 handler for unmatched routes
 */
const notFoundHandler = (req, res) => {
  res.status(404).json({
    error: 'Endpoint not found',
    details: [],
    backtrace: [],
  });
};

module.exports = {
  errorHandler,
  notFoundHandler,
};
```

## Creating Custom Middleware

### Basic Middleware Pattern

```javascript
// src/middlewares/customMiddleware.js

/**
 * Basic middleware template
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const customMiddleware = (req, res, next) => {
  try {
    // Middleware logic here

    // Modify request
    req.customProperty = 'value';

    // Modify response
    res.set('X-Custom-Header', 'value');

    // Continue to next middleware
    next();
  } catch (error) {
    // Pass error to error handler
    next(error);
  }
};

/**
 * Middleware factory pattern
 * @param {Object} options - Configuration options
 * @returns {Function} Express middleware function
 */
const createCustomMiddleware = (options = {}) => {
  const { setting1 = 'default', setting2 = true } = options;

  return (req, res, next) => {
    // Use options in middleware logic
    if (setting2) {
      req.customSetting = setting1;
    }

    next();
  };
};

module.exports = {
  customMiddleware,
  createCustomMiddleware,
};
```

### Request ID Middleware

```javascript
// src/middlewares/requestId.js
const { v4: uuidv4 } = require('uuid');

/**
 * Add unique request ID to each request
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const requestId = (req, res, next) => {
  // Generate unique request ID
  req.id = uuidv4();

  // Add to response headers
  res.set('X-Request-ID', req.id);

  next();
};

module.exports = { requestId };
```

### CORS Middleware (Custom)

```javascript
// src/middlewares/cors.js

/**
 * Custom CORS middleware
 * @param {Object} options - CORS options
 * @returns {Function} Express middleware function
 */
const createCorsMiddleware = (options = {}) => {
  const {
    origin = '*',
    methods = ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders = ['Content-Type', 'Authorization'],
    credentials = false,
  } = options;

  return (req, res, next) => {
    // Set CORS headers
    res.header('Access-Control-Allow-Origin', origin);
    res.header('Access-Control-Allow-Methods', methods.join(', '));
    res.header('Access-Control-Allow-Headers', allowedHeaders.join(', '));

    if (credentials) {
      res.header('Access-Control-Allow-Credentials', 'true');
    }

    // Handle preflight requests
    if (req.method === 'OPTIONS') {
      return res.status(200).end();
    }

    next();
  };
};

module.exports = { createCorsMiddleware };
```

## Middleware Order

The order of middleware is crucial. Here's the recommended order:

```javascript
// src/app.js
const express = require('express');
const { createLogger } = require('./middlewares/logger');
const { requestId } = require('./middlewares/requestId');
const { rateLimiters } = require('./middlewares/rateLimiter');
const { errorHandler, notFoundHandler } = require('./middlewares/errorHandler');

const app = express();

// 1. Request ID (first, so it's available in all subsequent middleware)
app.use(requestId);

// 2. Logging (early, to log all requests)
app.use(createLogger());

// 3. Security middleware
app.use(helmet());
app.use(cors());

// 4. Body parsing
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 5. Rate limiting
app.use(rateLimiters.general);

// 6. Routes
app.use('/api/v1/auth', rateLimiters.auth, authRoutes);
app.use('/api/v1/users', authenticateToken, userRoutes);

// 7. 404 handler (after all routes)
app.use(notFoundHandler);

// 8. Error handler (last)
app.use(errorHandler);
```

## Testing Middleware

### Testing Authentication Middleware

```javascript
// tests/middlewares/auth.test.js
const {
  authenticateToken,
  requireRole,
} = require('../../src/middlewares/auth');
const { User } = require('../../src/models');
const jwt = require('jsonwebtoken');

describe('Authentication Middleware', () => {
  let req, res, next;

  beforeEach(() => {
    req = {
      headers: {},
    };
    res = {};
    next = jest.fn();
  });

  describe('authenticateToken', () => {
    it('should authenticate valid token', async () => {
      const user = await User.create({
        email: '<EMAIL>',
        name: 'Test User',
        password: 'password123',
      });

      const token = jwt.sign({ userId: user.id }, process.env.JWT_SECRET);
      req.headers.authorization = `Bearer ${token}`;

      await authenticateToken(req, res, next);

      expect(req.user).toBeDefined();
      expect(req.user.id).toBe(user.id);
      expect(next).toHaveBeenCalledWith();
    });

    it('should reject invalid token', async () => {
      req.headers.authorization = 'Bearer invalid-token';

      await authenticateToken(req, res, next);

      expect(req.user).toBeUndefined();
      expect(next).toHaveBeenCalledWith(expect.any(Error));
    });
  });

  describe('requireRole', () => {
    it('should allow access for correct role', () => {
      req.user = { role: 'admin' };
      const middleware = requireRole('admin');

      middleware(req, res, next);

      expect(next).toHaveBeenCalledWith();
    });

    it('should deny access for incorrect role', () => {
      req.user = { role: 'user' };
      const middleware = requireRole('admin');

      middleware(req, res, next);

      expect(next).toHaveBeenCalledWith(expect.any(Error));
    });
  });
});
```

### Testing Rate Limiting

```javascript
// tests/middlewares/rateLimiter.test.js
const request = require('supertest');
const app = require('../../src/app');

describe('Rate Limiting', () => {
  it('should allow requests within limit', async () => {
    const response = await request(app).get('/api/v1/health');
    expect(response.status).toBe(200);
  });

  it('should block requests exceeding limit', async () => {
    // Make requests up to the limit
    const promises = Array(6)
      .fill()
      .map(() =>
        request(app).post('/api/v1/auth/login').send({
          email: '<EMAIL>',
          password: 'wrong',
        }),
      );

    const responses = await Promise.all(promises);

    // Last request should be rate limited
    const lastResponse = responses[responses.length - 1];
    expect(lastResponse.status).toBe(429);
    expect(lastResponse.body.error).toContain('Too many requests');
  });
});
```

## Best Practices

### 1. Order Middleware Correctly

```javascript
// ✅ Good - correct order
app.use(requestId); // First
app.use(logger); // Early
app.use(security); // Before routes
app.use(bodyParser); // Before routes
app.use(routes); // Routes
app.use(notFoundHandler); // After routes
app.use(errorHandler); // Last
```

### 2. Handle Errors Properly

```javascript
// ✅ Good - pass errors to next()
const middleware = (req, res, next) => {
  try {
    // Middleware logic
    next();
  } catch (error) {
    next(error); // Pass to error handler
  }
};

// ❌ Bad - swallow errors
const middleware = (req, res, next) => {
  try {
    // Middleware logic
    next();
  } catch (error) {
    console.log(error); // Error is lost
    next();
  }
};
```

### 3. Use Middleware Factories for Configuration

```javascript
// ✅ Good - configurable middleware
const createAuthMiddleware = (options = {}) => {
  return (req, res, next) => {
    // Use options
    next();
  };
};

// ❌ Bad - hardcoded middleware
const authMiddleware = (req, res, next) => {
  // Hardcoded behavior
  next();
};
```

### 4. Keep Middleware Focused

```javascript
// ✅ Good - single responsibility
const authenticateToken = (req, res, next) => {
  // Only handles token authentication
};

const requireRole = role => (req, res, next) => {
  // Only handles role authorization
};

// ❌ Bad - multiple responsibilities
const authMiddleware = (req, res, next) => {
  // Handles authentication AND authorization AND logging
};
```

### 5. Document Middleware Behavior

```javascript
/**
 * Authenticate JWT token and attach user to request
 *
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 *
 * @throws {UnauthorizedError} When token is missing or invalid
 * @throws {ForbiddenError} When user account is deactivated
 *
 * @example
 * router.get('/profile', authenticateToken, controller.profile);
 */
const authenticateToken = async (req, res, next) => {
  // Implementation
};
```

```

```
