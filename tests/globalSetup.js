require('dotenv').config({ path: '.env.test', quiet: true });

// Set test environment
process.env.NODE_ENV = 'test';

const { sequelize } = require('../src/config/sequelize');

module.exports = async () => {
  try {
    console.log('Setting up test database...');

    // Ensure database connection
    await sequelize.authenticate();
    console.log('Test database connection established');

    // Drop and recreate all tables to ensure clean state
    await sequelize.drop({ cascade: true });
    await sequelize.sync({ force: true });
    console.log('Test database schema synchronized');

    // Don't close the connection here - let individual tests manage it
    console.log('Global setup complete');
  } catch (error) {
    console.error('Failed to setup test database:', error);
    throw error;
  }
};
