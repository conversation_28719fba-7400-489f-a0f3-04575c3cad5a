const { JobTitle } = require('../../src/models');

describe('JobTitle Model', () => {
  beforeEach(async () => {
    // Clean up job titles before each test
    await JobTitle.destroy({ where: {}, truncate: true, cascade: true });
  });

  describe('validations', () => {
    it('should create a valid job title with required fields', async () => {
      const jobTitleData = {
        name: 'Software Engineer',
        prefilled_details: {
          department: 'Engineering',
          level: 'Mid-level',
          skills: ['JavaScript', 'Node.js'],
        },
      };

      const jobTitle = await JobTitle.create(jobTitleData);

      expect(jobTitle.id).toBeDefined();
      expect(jobTitle.name).toBe('Software Engineer');
      expect(jobTitle.prefilled_details).toEqual({
        department: 'Engineering',
        level: 'Mid-level',
        skills: ['JavaScript', 'Node.js'],
      });
      expect(jobTitle.created_at).toBeDefined();
      expect(jobTitle.updated_at).toBeDefined();
    });

    it('should create a job title without prefilled details', async () => {
      const jobTitleData = {
        name: 'Sales Representative',
        prefilled_details: null,
      };

      const jobTitle = await JobTitle.create(jobTitleData);

      expect(jobTitle.id).toBeDefined();
      expect(jobTitle.name).toBe('Sales Representative');
      expect(jobTitle.prefilled_details).toBeNull();
    });

    it('should fail validation if name is missing', async () => {
      const jobTitleData = {
        prefilled_details: { department: 'Engineering' },
      };

      await expect(JobTitle.create(jobTitleData)).rejects.toThrow();
    });

    it('should fail validation if name is empty', async () => {
      const jobTitleData = {
        name: '',
        prefilled_details: { department: 'Engineering' },
      };

      await expect(JobTitle.create(jobTitleData)).rejects.toThrow();
    });

    it('should fail validation if name is too short', async () => {
      const jobTitleData = {
        name: 'A',
        prefilled_details: { department: 'Engineering' },
      };

      await expect(JobTitle.create(jobTitleData)).rejects.toThrow();
    });

    it('should fail validation if name is too long', async () => {
      const jobTitleData = {
        name: 'A'.repeat(101), // 101 characters
        prefilled_details: { department: 'Engineering' },
      };

      await expect(JobTitle.create(jobTitleData)).rejects.toThrow();
    });

    it('should enforce unique constraint on name', async () => {
      const jobTitleData = {
        name: 'Software Engineer',
        prefilled_details: { department: 'Engineering' },
      };

      await JobTitle.create(jobTitleData);

      // Try to create another job title with the same name
      await expect(JobTitle.create(jobTitleData)).rejects.toThrow();
    });

    it('should fail validation if prefilled_details is not a valid JSON object', async () => {
      const jobTitleData = {
        name: 'Software Engineer',
        prefilled_details: 'invalid json string',
      };

      await expect(JobTitle.create(jobTitleData)).rejects.toThrow();
    });
  });

  describe('hooks', () => {
    it('should set created_at and updated_at on create', async () => {
      const beforeCreate = new Date();

      const jobTitle = await JobTitle.create({
        name: 'Product Manager',
        prefilled_details: { department: 'Product' },
      });

      const afterCreate = new Date();

      expect(jobTitle.created_at).toBeDefined();
      expect(jobTitle.updated_at).toBeDefined();
      expect(jobTitle.created_at.getTime()).toBeGreaterThanOrEqual(beforeCreate.getTime());
      expect(jobTitle.created_at.getTime()).toBeLessThanOrEqual(afterCreate.getTime());
      expect(jobTitle.updated_at.getTime()).toBeGreaterThanOrEqual(beforeCreate.getTime());
      expect(jobTitle.updated_at.getTime()).toBeLessThanOrEqual(afterCreate.getTime());
    });

    it('should update updated_at on update', async () => {
      const jobTitle = await JobTitle.create({
        name: 'Marketing Manager',
        prefilled_details: { department: 'Marketing' },
      });

      const originalUpdatedAt = jobTitle.updated_at;

      // Wait a bit to ensure timestamp difference
      await new Promise(resolve => setTimeout(resolve, 10));

      jobTitle.name = 'Senior Marketing Manager';
      await jobTitle.save();

      expect(jobTitle.updated_at.getTime()).toBeGreaterThan(originalUpdatedAt.getTime());
    });
  });

  describe('model behavior', () => {
    it('should handle complex prefilled details structure', async () => {
      const complexDetails = {
        department: 'Engineering',
        level: 'Senior',
        skills: ['JavaScript', 'Node.js', 'React', 'System Design'],
        experience_required: '5+ years',
        responsibilities: [
          'Lead technical projects',
          'Mentor junior developers',
          'Design system architecture',
        ],
        benefits: {
          salary_range: '$120k-$150k',
          remote_work: true,
          health_insurance: true,
        },
      };

      const jobTitle = await JobTitle.create({
        name: 'Senior Software Engineer',
        prefilled_details: complexDetails,
      });

      expect(jobTitle.prefilled_details).toEqual(complexDetails);
    });

    it('should find job titles by name', async () => {
      await JobTitle.create({
        name: 'Data Scientist',
        prefilled_details: { department: 'Data' },
      });

      const foundJobTitle = await JobTitle.findOne({
        where: { name: 'Data Scientist' },
      });

      expect(foundJobTitle).toBeDefined();
      expect(foundJobTitle.name).toBe('Data Scientist');
    });

    it('should count job titles correctly', async () => {
      await JobTitle.bulkCreate([
        { name: 'UX Designer', prefilled_details: { department: 'Design' } },
        { name: 'DevOps Engineer', prefilled_details: { department: 'Engineering' } },
        { name: 'Sales Representative', prefilled_details: null },
      ]);

      const count = await JobTitle.count();
      expect(count).toBe(3);
    });
  });
});
