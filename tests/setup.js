require('dotenv').config({ path: '.env.test', quiet: true });

// Set test environment
process.env.NODE_ENV = 'test';

const { sequelize } = require('../src/config/sequelize');

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  // Uncomment to suppress console.log in tests
  // log: jest.fn(),
  // info: jest.fn(),
  // warn: jest.fn(),
  // error: jest.fn(),
};

// Global test timeout
jest.setTimeout(30000);

// Clean up after each test
afterEach(async () => {
  jest.clearAllMocks();
});

beforeAll(async () => {
  // Ensure database connection is available
  try {
    await sequelize.authenticate();
  } catch (error) {
    console.error('Failed to connect to test database:', error);
    throw error;
  }
});

afterAll(async () => {
  // Close database connection after all tests
  try {
    await sequelize.close();
  } catch (error) {
    console.error('Error closing database connection:', error);
  }
});
