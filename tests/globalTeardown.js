const { sequelize } = require('../src/config/sequelize');

module.exports = async () => {
  try {
    console.log('Cleaning up test database...');

    // Ensure we have a connection
    if (!sequelize.connectionManager.pool) {
      await sequelize.authenticate();
    }

    // Close database connection
    await sequelize.close();
    console.log('Test database connection closed');
  } catch (error) {
    console.error('Failed to close test database connection:', error);
    // Don't throw error in teardown to avoid masking test failures
  }
};
