const request = require('supertest');
const app = require('../../src/app');
const { User } = require('../../src/models');

describe('UsersController', () => {
  let adminToken;
  let userToken;

  beforeEach(async () => {
    // Clean database before each test
    await User.destroy({ where: {}, truncate: true, cascade: true });

    // Create admin user
    await User.create({
      name: 'Admin User',
      email: '<EMAIL>',
      password: 'password123',
      role: 'admin',
    });

    // Create regular user
    await User.create({
      name: 'Regular User',
      email: '<EMAIL>',
      password: 'password123',
      role: 'user',
    });

    // Get tokens via login endpoint
    const adminLoginResponse = await request(app)
      .post('/api/v1/auth/login')
      .send({ email: '<EMAIL>', password: 'password123' });
    adminToken = adminLoginResponse.body.data.authToken;

    const userLoginResponse = await request(app)
      .post('/api/v1/auth/login')
      .send({ email: '<EMAIL>', password: 'password123' });
    userToken = userLoginResponse.body.data.authToken;
  });

  describe('GET /api/v1/users', () => {
    beforeEach(async () => {
      // Create additional test users
      await User.bulkCreate(
        [
          { name: 'John Doe', email: '<EMAIL>', password: 'password', role: 'admin' },
          { name: 'Jane Smith', email: '<EMAIL>', password: 'password', role: 'user' },
          { name: 'Bob Johnson', email: '<EMAIL>', password: 'password', role: 'user' },
          { name: 'Alice Brown', email: '<EMAIL>', password: 'password', role: 'admin' },
        ],
        { individualHooks: true },
      );
    });

    describe('authentication and authorization', () => {
      it('should return 401 without token', async () => {
        const response = await request(app).get('/api/v1/users');

        expect(response.status).toBe(401);
        expect(response.body).toHaveProperty('error', 'Access token required');
      });

      it('should return 403 for non-admin users', async () => {
        const response = await request(app)
          .get('/api/v1/users')
          .set('Authorization', `Bearer ${userToken}`);

        expect(response.status).toBe(403);
        expect(response.body).toHaveProperty('error', 'Insufficient permissions');
      });

      it('should return 200 for admin users', async () => {
        const response = await request(app)
          .get('/api/v1/users')
          .set('Authorization', `Bearer ${adminToken}`);

        expect(response.status).toBe(200);
        expect(response.body).toHaveProperty('data');
        expect(Array.isArray(response.body.data)).toBe(true);
      });
    });

    describe('basic functionality', () => {
      it('should return all users with pagination', async () => {
        const response = await request(app)
          .get('/api/v1/users')
          .set('Authorization', `Bearer ${adminToken}`);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(6); // 2 initial + 4 created in beforeEach
        expect(response.body).toHaveProperty('pagination');
        expect(response.body.pagination).toMatchObject({
          page: 1,
          limit: 10,
          total: 6,
          totalPages: 1,
        });
      });

      it('should handle pagination parameters', async () => {
        const response = await request(app)
          .get('/api/v1/users?page=1&limit=3')
          .set('Authorization', `Bearer ${adminToken}`);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(3);
        expect(response.body.pagination).toMatchObject({
          page: 1,
          limit: 3,
          total: 6,
          totalPages: 2,
        });
      });
    });

    describe('filtering', () => {
      it('should filter users by role', async () => {
        const response = await request(app)
          .get('/api/v1/users?role=admin')
          .set('Authorization', `Bearer ${adminToken}`);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(3); // Admin User, John Doe, Alice Brown
        expect(response.body.data.every(user => user.role === 'admin')).toBe(true);
      });

      it('should filter users by name', async () => {
        const response = await request(app)
          .get('/api/v1/users?name=John')
          .set('Authorization', `Bearer ${adminToken}`);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(2); // John Doe, Bob Johnson
        expect(response.body.data.some(user => user.name.includes('John'))).toBe(true);
      });

      it('should filter users by email domain', async () => {
        const response = await request(app)
          .get('/api/v1/users?email=<EMAIL>')
          .set('Authorization', `Bearer ${adminToken}`);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(1); // Jane Smith
        expect(response.body.data[0].email).toBe('<EMAIL>');
      });

      it('should search users by name and email', async () => {
        const response = await request(app)
          .get('/api/v1/users?search=john')
          .set('Authorization', `Bearer ${adminToken}`);

        expect(response.status).toBe(200);
        expect(response.body.data.length).toBeGreaterThan(0);
        expect(
          response.body.data.every(
            user =>
              user.name.toLowerCase().includes('john') || user.email.toLowerCase().includes('john'),
          ),
        ).toBe(true);
      });

      it('should combine multiple filters', async () => {
        const response = await request(app)
          .get('/api/v1/users?role=admin&name=John')
          .set('Authorization', `Bearer ${adminToken}`);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(1);
        expect(response.body.data[0].name).toBe('John Doe');
        expect(response.body.data[0].role).toBe('admin');
      });
    });

    describe('sorting', () => {
      it('should sort users by name ascending', async () => {
        const response = await request(app)
          .get('/api/v1/users?sort=name&sort_direction=asc')
          .set('Authorization', `Bearer ${adminToken}`);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(6);

        const names = response.body.data.map(user => user.name);
        const sortedNames = [...names].sort();
        expect(names).toEqual(sortedNames);
      });

      it('should sort users by name descending', async () => {
        const response = await request(app)
          .get('/api/v1/users?sort=name&sort_direction=desc')
          .set('Authorization', `Bearer ${adminToken}`);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(6);

        const names = response.body.data.map(user => user.name);
        const sortedNames = [...names].sort().reverse();
        expect(names).toEqual(sortedNames);
      });

      it('should sort users by email', async () => {
        const response = await request(app)
          .get('/api/v1/users?sort=email&sort_direction=asc')
          .set('Authorization', `Bearer ${adminToken}`);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(6);

        const emails = response.body.data.map(user => user.email);
        const sortedEmails = [...emails].sort();
        expect(emails).toEqual(sortedEmails);
      });

      it('reject invalid sort columns', async () => {
        const response = await request(app)
          .get('/api/v1/users?sort=invalid_column&sort_direction=asc')
          .set('Authorization', `Bearer ${adminToken}`);

        expect(response.status).toBe(400);
        expect(response.body).toHaveProperty('error', 'Validation failed');
      });
    });

    describe('date filtering', () => {
      it('should filter users by created_after date', async () => {
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        const dateString = yesterday.toISOString().split('T')[0];

        const response = await request(app)
          .get(`/api/v1/users?created_after=${dateString}`)
          .set('Authorization', `Bearer ${adminToken}`);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(6); // All users created today
      });

      it('should filter users by created_before date', async () => {
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        const dateString = tomorrow.toISOString().split('T')[0];

        const response = await request(app)
          .get(`/api/v1/users?created_before=${dateString}`)
          .set('Authorization', `Bearer ${adminToken}`);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(6); // All users created today
      });
    });

    describe('complex queries', () => {
      it('should handle complex query with multiple parameters', async () => {
        const response = await request(app)
          .get('/api/v1/users?role=user&page=1&limit=2&sort=name&sort_direction=asc')
          .set('Authorization', `Bearer ${adminToken}`);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(2);
        expect(response.body.data.every(user => user.role === 'user')).toBe(true);
        expect(response.body.pagination).toMatchObject({
          page: 1,
          limit: 2,
          total: 3, // Regular User, Jane Smith, Bob Johnson
          totalPages: 2,
        });
      });
    });

    describe('response format', () => {
      it('should return properly formatted user data', async () => {
        const response = await request(app)
          .get('/api/v1/users?limit=1')
          .set('Authorization', `Bearer ${adminToken}`);

        expect(response.status).toBe(200);
        expect(response.body.data).toHaveLength(1);

        const user = response.body.data[0];
        expect(user).toHaveProperty('id');
        expect(user).toHaveProperty('name');
        expect(user).toHaveProperty('email');
        expect(user).toHaveProperty('role');
        expect(user).not.toHaveProperty('password');
        expect(user).not.toHaveProperty('password_digest');
      });
    });
  });
});
