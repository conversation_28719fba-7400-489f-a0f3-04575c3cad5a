const BaseRepository = require('../../src/repositories/BaseRepository');
const { User } = require('../../src/models');

// Create a test repository class for testing BaseRepository
class TestRepository extends BaseRepository {
  constructor() {
    super(User);
  }

  defaultScope() {
    return {}; // No default filtering for tests
  }

  // Test filter method
  filterByRole(role) {
    if (!role) return null;
    return { role };
  }

  filterByName(name) {
    if (!name) return null;
    return { name: { [require('sequelize').Op.iLike]: `%${name}%` } };
  }
}

describe('BaseRepository', () => {
  let repository;

  beforeEach(async () => {
    repository = new TestRepository();
    await User.destroy({ where: {}, truncate: true, cascade: true });
  });

  describe('constructor', () => {
    it('should set model property when valid model provided', () => {
      const repo = new BaseRepository(User);
      expect(repo.model).toBe(User);
    });
  });

  describe('findAll', () => {
    beforeEach(async () => {
      // Create test users
      await User.bulkCreate(
        [
          { name: '<PERSON> <PERSON>e', email: '<EMAIL>', password: 'password', role: 'admin' },
          { name: 'Jane Smith', email: '<EMAIL>', password: 'password', role: 'user' },
          { name: 'Bob Johnson', email: '<EMAIL>', password: 'password', role: 'user' },
          { name: 'Alice Brown', email: '<EMAIL>', password: 'password', role: 'admin' },
        ],
        { individualHooks: true },
      );
    });

    it('should return all records with default pagination', async () => {
      const result = await repository.findAll();

      expect(result.rows).toHaveLength(4);
      expect(result.pagination).toMatchObject({
        page: 1,
        limit: 10,
        total: 4,
      });
    });

    it('should handle pagination correctly', async () => {
      const result = await repository.findAll({ page: 1, limit: 2 });

      expect(result.rows).toHaveLength(2);
      expect(result.pagination).toMatchObject({
        page: 1,
        limit: 2,
        total: 4,
      });
    });

    it('should handle second page pagination', async () => {
      const result = await repository.findAll({ page: 2, limit: 2 });

      expect(result.rows).toHaveLength(2);
      expect(result.pagination).toMatchObject({
        page: 2,
        limit: 2,
        total: 4,
      });
    });

    it('should apply filters using filterBy methods', async () => {
      const result = await repository.findAll({ role: 'admin' });

      expect(result.rows).toHaveLength(2);
      expect(result.rows.every(user => user.role === 'admin')).toBe(true);
    });

    it('should apply multiple filters', async () => {
      const result = await repository.findAll({ role: 'admin', name: 'John' });

      expect(result.rows).toHaveLength(1);
      expect(result.rows[0].name).toBe('John Doe');
      expect(result.rows[0].role).toBe('admin');
    });

    it('should handle sorting by column', async () => {
      const result = await repository.findAll({ sort: 'name', sort_direction: 'asc' });

      expect(result.rows).toHaveLength(4);
      expect(result.rows[0].name).toBe('Alice Brown');
      expect(result.rows[1].name).toBe('Bob Johnson');
      expect(result.rows[2].name).toBe('Jane Smith');
      expect(result.rows[3].name).toBe('John Doe');
    });

    it('should handle descending sort', async () => {
      const result = await repository.findAll({ sort: 'name', sort_direction: 'desc' });

      expect(result.rows).toHaveLength(4);
      expect(result.rows[0].name).toBe('John Doe');
      expect(result.rows[3].name).toBe('Alice Brown');
    });

    it('should ignore invalid sort columns', async () => {
      const result = await repository.findAll({ sort: 'invalid_column', sort_direction: 'asc' });

      expect(result.rows).toHaveLength(4);
      // Should fall back to default sorting (created_at DESC)
    });
  });

  describe('count', () => {
    beforeEach(async () => {
      await User.bulkCreate(
        [
          { name: 'John Doe', email: '<EMAIL>', password: 'password', role: 'admin' },
          { name: 'Jane Smith', email: '<EMAIL>', password: 'password', role: 'user' },
          { name: 'Bob Johnson', email: '<EMAIL>', password: 'password', role: 'user' },
        ],
        { individualHooks: true },
      );
    });

    it('should count all records', async () => {
      const count = await repository.count();
      expect(count).toBe(3);
    });

    it('should count filtered records', async () => {
      const count = await repository.count({ role: 'user' });
      expect(count).toBe(2);
    });
  });

  describe('exists', () => {
    let testUser;

    beforeEach(async () => {
      testUser = await User.create({
        name: 'Test User',
        email: '<EMAIL>',
        password: 'password',
        role: 'user',
      });
    });

    it('should return true for existing record', async () => {
      const exists = await repository.exists(testUser.id);
      expect(exists).toBe(true);
    });

    it('should return false for non-existent record', async () => {
      const exists = await repository.exists(99999);
      expect(exists).toBe(false);
    });
  });

  describe('utility methods', () => {
    describe('capitalize', () => {
      it('should capitalize first letter', () => {
        expect(repository.capitalize('hello')).toBe('Hello');
        expect(repository.capitalize('WORLD')).toBe('WORLD');
        expect(repository.capitalize('a')).toBe('A');
      });
    });

    describe('isValidSortColumn', () => {
      it('should return true for valid model attributes', () => {
        expect(repository.isValidSortColumn('name')).toBe(true);
        expect(repository.isValidSortColumn('email')).toBe(true);
        expect(repository.isValidSortColumn('created_at')).toBe(true);
      });

      it('should return false for invalid columns', () => {
        expect(repository.isValidSortColumn('invalid_column')).toBe(false);
        expect(repository.isValidSortColumn('nonexistent')).toBe(false);
      });
    });
  });
});
