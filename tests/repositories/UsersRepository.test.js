const UsersRepository = require('../../src/repositories/UsersRepository');
const { User } = require('../../src/models');

describe('UsersRepository', () => {
  let repository;

  beforeEach(async () => {
    repository = new UsersRepository();
    await User.destroy({ where: {}, truncate: true, cascade: true });
  });

  describe('constructor', () => {
    it('should initialize with User model', () => {
      expect(repository.model).toBe(User);
    });
  });

  describe('filter methods', () => {
    beforeEach(async () => {
      await User.bulkCreate(
        [
          { name: '<PERSON>', email: '<EMAIL>', password: 'password', role: 'admin' },
          { name: '<PERSON>', email: '<EMAIL>', password: 'password', role: 'user' },
          { name: '<PERSON>', email: '<EMAIL>', password: 'password', role: 'user' },
          { name: '<PERSON>', email: '<EMAIL>', password: 'password', role: 'admin' },
        ],
        { individualHooks: true },
      );
    });

    describe('filterByRole', () => {
      it('should filter users by role', async () => {
        const result = await repository.findAll({ role: 'admin' });

        expect(result.rows).toHaveLength(2);
        expect(result.rows.every(user => user.role === 'admin')).toBe(true);
      });

      it('should return null for empty role', () => {
        const filter = repository.filterByRole('');
        expect(filter).toBeNull();
      });

      it('should handle case insensitive role filtering', async () => {
        const result = await repository.findAll({ role: 'ADMIN' });

        expect(result.rows).toHaveLength(2);
        expect(result.rows.every(user => user.role === 'admin')).toBe(true);
      });
    });

    describe('filterByName', () => {
      it('should filter users by partial name match', async () => {
        const result = await repository.findAll({ name: 'John' });

        expect(result.rows).toHaveLength(2); // John Doe and Bob Johnson
        expect(result.rows.some(user => user.name.includes('John'))).toBe(true);
      });

      it('should be case insensitive', async () => {
        const result = await repository.findAll({ name: 'john' });

        expect(result.rows).toHaveLength(2);
        expect(result.rows.some(user => user.name.includes('John'))).toBe(true);
      });

      it('should return null for empty name', () => {
        const filter = repository.filterByName('');
        expect(filter).toBeNull();
      });
    });

    describe('filterByEmail', () => {
      it('should filter users by partial email match', async () => {
        const result = await repository.findAll({ email: 'example.com' });

        expect(result.rows).toHaveLength(2); // john@example.<NAME_EMAIL>
        expect(result.rows.every(user => user.email.includes('example.com'))).toBe(true);
      });

      it('should be case insensitive', async () => {
        const result = await repository.findAll({ email: 'EXAMPLE.COM' });

        expect(result.rows).toHaveLength(2);
        expect(result.rows.every(user => user.email.includes('example.com'))).toBe(true);
      });

      it('should return null for empty email', () => {
        const filter = repository.filterByEmail('');
        expect(filter).toBeNull();
      });
    });

    describe('filterBySearch', () => {
      it('should search in both name and email', async () => {
        const result = await repository.findAll({ search: 'john' });

        expect(result.rows).toHaveLength(2); // John Doe, Bob Johnson (contains 'john')
        expect(
          result.rows.every(
            user =>
              user.name.toLowerCase().includes('john') || user.email.toLowerCase().includes('john'),
          ),
        ).toBe(true);
      });

      it('should return null for empty search', () => {
        const filter = repository.filterBySearch('');
        expect(filter).toBeNull();
      });
    });

    describe('filterByCreatedAfter', () => {
      it('should filter users created after date', async () => {
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);

        const result = await repository.findAll({
          created_after: yesterday.toISOString().split('T')[0],
        });

        expect(result.rows).toHaveLength(4); // All users created today
      });

      it('should return null for invalid date', () => {
        const filter = repository.filterByCreatedAfter('invalid-date');
        expect(filter).toBeNull();
      });

      it('should return null for empty date', () => {
        const filter = repository.filterByCreatedAfter('');
        expect(filter).toBeNull();
      });
    });

    describe('filterByCreatedBefore', () => {
      it('should filter users created before date', async () => {
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);

        const result = await repository.findAll({
          created_before: tomorrow.toISOString().split('T')[0],
        });

        expect(result.rows).toHaveLength(4); // All users created today
      });

      it('should return null for invalid date', () => {
        const filter = repository.filterByCreatedBefore('invalid-date');
        expect(filter).toBeNull();
      });
    });
  });

  describe('buildOrderClause', () => {
    it('should use allowed sort columns', () => {
      const orderClause = repository.buildOrderClause('name', 'asc');
      expect(orderClause).toEqual([['name', 'ASC']]);
    });

    it('should reject invalid sort columns', () => {
      const orderClause = repository.buildOrderClause('invalid_column', 'asc');
      expect(orderClause).toEqual([['created_at', 'DESC']]); // Default
    });

    it('should handle desc sorting', () => {
      const orderClause = repository.buildOrderClause('email', 'desc');
      expect(orderClause).toEqual([['email', 'DESC']]);
    });

    it('should default to ASC when sort direction is invalid', () => {
      const orderClause = repository.buildOrderClause('name', 'invalid');
      expect(orderClause).toEqual([['name', 'ASC']]);
    });
  });

  describe('integration with query parameters', () => {
    beforeEach(async () => {
      // Create users with different timestamps
      const baseDate = new Date('2024-01-01');

      await User.create({
        name: 'Old Admin',
        email: '<EMAIL>',
        password: 'password',
        role: 'admin',
        created_at: new Date(baseDate.getTime() - 86400000), // 1 day before
      });

      await User.create({
        name: 'New User',
        email: '<EMAIL>',
        password: 'password',
        role: 'user',
        created_at: baseDate,
      });
    });

    it('should handle complex query combinations', async () => {
      const queryParams = {
        role: 'admin',
        name: 'Old',
        page: 1,
        limit: 5,
        sort: 'name',
        sort_direction: 'asc',
      };

      const result = await repository.findAll(queryParams);

      expect(result.rows).toHaveLength(1);
      expect(result.rows[0].name).toBe('Old Admin');
      expect(result.rows[0].role).toBe('admin');
    });

    it('should handle date range filtering', async () => {
      // Since the User model always sets created_at to current date,
      // we test with today's date range
      const today = new Date();
      const yesterday = new Date(today.getTime() - 86400000);
      const tomorrow = new Date(today.getTime() + 86400000);

      const result = await repository.findAll({
        created_after: yesterday.toISOString().split('T')[0],
        created_before: tomorrow.toISOString().split('T')[0],
      });

      expect(result.rows).toHaveLength(2); // Both users created today
      expect(result.rows.some(user => user.name === 'New User')).toBe(true);
    });
  });
});
